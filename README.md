# Edara Electron App

Application Electron pour encapsuler le service Odoo 13 Edara avec gestion intelligente des connexions locales et distantes.

## 🚀 Fonctionnalités

- **Connexion Intelligente** : Priorité aux serveurs locaux avec fallback automatique vers le serveur distant
- **Interface Moderne** : Interface de connexion élégante avec support des thèmes sombre/clair
- **Écran de Chargement** : Animation de progression pendant le chargement d'Odoo
- **Gestion des Sessions** : Authentification sécurisée et gestion des cookies de session
- **Multi-Serveurs** : Support de plusieurs serveurs locaux configurables

## 🛠️ Installation

1. **Installer les dépendances** :
```bash
npm install
```

2. **Configurer les serveurs locaux** :
Éditer le fichier `serveur_ip.txt` avec vos adresses IP locales :
```
**************
************
************
```

## 🚀 Utilisation

### Démarrage de l'application
```bash
npm start
```

### Construction pour distribution
```bash
# Pour toutes les plateformes
npm run build

# Pour Windows uniquement
npm run build-win

# Pour macOS uniquement
npm run build-mac

# Pour Linux uniquement
npm run build-linux
```

## ⚙️ Configuration

### Serveurs Locaux
Modifiez le fichier `serveur_ip.txt` pour ajouter vos serveurs locaux :
```
**************
************
************
```

### Serveur Distant
Par défaut : `https://edara.ligne-digitale.com`

### Base de Données
Par défaut : `ligne-digitale`

## 🔄 Logique de Connexion

1. **Test des serveurs locaux** : Test séquentiel des serveurs dans `serveur_ip.txt` (port 8069)
2. **Fallback distant** : Si aucun serveur local disponible, connexion au serveur distant
3. **Authentification** : Via API XML-RPC d'Odoo
4. **Interface** : Chargement dans une fenêtre dédiée avec détection automatique

## 🔒 Sécurité

- **Context Isolation** : Activé pour toutes les fenêtres
- **Node Integration** : Désactivé côté renderer
- **Preload Script** : Communication sécurisée via contextBridge
- **Web Security** : Désactivé uniquement pour la fenêtre Odoo (nécessaire)

## 📦 Distribution

Les fichiers de distribution sont générés dans le dossier `dist/` :
- **Windows** : Installateur NSIS (.exe)
- **macOS** : Image disque (.dmg)
- **Linux** : AppImage (.AppImage)

## 📄 Licence

MIT License

{"name": "edara-electron-app", "version": "1.0.0", "description": "Application Electron pour encapsuler le service Odoo 13 Edara", "main": "main.js", "scripts": {"start": "electron .", "start-optimized": "node start-optimized.js", "build": "electron-builder", "build-win": "electron-builder --win", "build-mac": "electron-builder --mac", "build-linux": "electron-builder --linux", "pack": "electron-builder --dir", "dist": "electron-builder --publish=never"}, "keywords": ["electron", "odoo", "erp", "edara"], "author": "Edara Team", "license": "MIT", "devDependencies": {"electron": "^27.0.0", "electron-builder": "^24.6.4"}, "dependencies": {"axios": "^1.9.0", "electron-log": "^4.4.8"}, "build": {"appId": "com.edara.electron-app", "productName": "Edara ERP", "directories": {"output": "dist"}, "files": ["main.js", "preload.js", "src/**/*", "index-final.html", "splash-final.html", "style-final.css", "img/**/*", "serveur_ip.txt", "config/**/*"], "mac": {"category": "public.app-category.business", "icon": "img/icon.icns"}, "win": {"target": "nsis", "icon": "img/icon.ico"}, "linux": {"target": "AppImage", "icon": "img/icon.png"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true}}, "repository": {"type": "git", "url": "https://github.com/edara/electron-app.git"}, "bugs": {"url": "https://github.com/edara/electron-app/issues"}, "homepage": "https://github.com/edara/electron-app#readme"}
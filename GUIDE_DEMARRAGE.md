# 🚀 Guide de Démarrage Rapide - Edara Electron App

## ✅ Installation et Premier Lancement

### 1. Prérequis
- Node.js version 16 ou supérieure
- npm (inclus avec Node.js)
- Accès réseau aux serveurs Odoo

### 2. Installation des dépendances
```bash
npm install
```

### 3. Vérification de l'environnement
```bash
npm run check
```

### 4. Tests de l'application
```bash
# Tests des modules
npm test

# Tests Electron
npm run test-electron

# Tous les tests
npm run test-all
```

### 5. Lancement de l'application
```bash
# Mode production
npm start

# Mode développement
npm run dev

# Mode développement avec outils de développement
npm run dev-tools
```

## 🔧 Configuration

### Serveurs Locaux
Modifiez le fichier `serveur_ip.txt` :
```
**************
************
************
```

### Serveur Distant
Par défaut : `https://edara.ligne-digitale.com`
(Modifiable dans `src/main/connection-manager.js`)

### Base de Données
Par défaut : `ligne-digitale`
(Modifiable dans `src/main/odoo-auth.js`)

## 🎯 Utilisation

### Interface de Connexion
1. **Sélection du type de connexion** :
   - **Local** : Utilise les serveurs configurés dans `serveur_ip.txt`
   - **Distance** : Utilise le serveur distant HTTPS

2. **Authentification** :
   - Saisissez votre email/nom d'utilisateur
   - Saisissez votre mot de passe
   - Cliquez sur "Se connecter"

3. **Processus de connexion** :
   - L'application teste automatiquement les serveurs disponibles
   - Affiche un écran de chargement pendant l'authentification
   - Charge l'interface Odoo une fois connecté

### Stratégie de Connexion
1. **Priorité locale** : Test séquentiel des serveurs locaux (port 8069)
2. **Fallback distant** : Si aucun serveur local disponible
3. **Authentification** : Via API XML-RPC d'Odoo
4. **Interface** : Chargement dans une fenêtre dédiée

## 🛠️ Développement

### Structure du Projet
```
edara-electron-app/
├── main.js                    # Point d'entrée principal
├── preload.js                 # Script de préchargement
├── index-final.html          # Interface de connexion
├── splash-final.html         # Écran de chargement
├── src/
│   ├── main/                 # Modules processus principal
│   └── renderer/             # Scripts interface utilisateur
├── config/                   # Configuration
├── scripts/                  # Scripts utilitaires
└── img/                      # Ressources
```

### Scripts Disponibles
- `npm start` : Lancer l'application
- `npm run dev` : Mode développement
- `npm run dev-tools` : Avec outils de développement
- `npm test` : Tests des modules
- `npm run test-electron` : Tests Electron
- `npm run check` : Vérification environnement
- `npm run build` : Construction pour distribution

### Logs
- **Console** : Niveau debug en développement
- **Fichier** : Sauvegardé dans le dossier logs de l'OS
- **Localisation** : 
  - macOS: `~/Library/Logs/edara-electron-app/`
  - Windows: `%USERPROFILE%\AppData\Roaming\edara-electron-app\logs\`
  - Linux: `~/.config/edara-electron-app/logs/`

## 🔒 Sécurité

### Mesures Implémentées
- **Context Isolation** : Activé pour toutes les fenêtres
- **Node Integration** : Désactivé côté renderer
- **Preload Script** : Communication sécurisée via contextBridge
- **Web Security** : Désactivé uniquement pour la fenêtre Odoo

### Authentification
- Utilisation de l'API XML-RPC officielle d'Odoo
- Gestion sécurisée des cookies de session
- Pas de stockage des mots de passe

## 🐛 Dépannage

### Problèmes Courants

#### 1. Erreur "Cannot find module"
```bash
npm install
```

#### 2. Serveurs locaux non accessibles
- Vérifiez que les serveurs Odoo sont démarrés
- Contrôlez les adresses IP dans `serveur_ip.txt`
- Testez la connectivité réseau

#### 3. Échec d'authentification
- Vérifiez les identifiants utilisateur
- Contrôlez que la base de données existe
- Vérifiez les logs pour plus de détails

#### 4. Interface Odoo ne se charge pas
- Vérifiez la connectivité au serveur
- Contrôlez les cookies de session
- Redémarrez l'application

### Commandes de Diagnostic
```bash
# Vérifier l'environnement
npm run check

# Tester les modules
npm test

# Tester Electron
npm run test-electron

# Nettoyer le cache
npm run clean
```

## 📦 Distribution

### Construction
```bash
# Toutes les plateformes
npm run build

# Windows uniquement
npm run build-win

# macOS uniquement
npm run build-mac

# Linux uniquement
npm run build-linux
```

### Fichiers Générés
- **Windows** : `dist/edara-electron-app Setup.exe`
- **macOS** : `dist/Edara ERP.dmg`
- **Linux** : `dist/edara-electron-app.AppImage`

## 📞 Support

### Logs et Débogage
1. Activez les outils de développement : `npm run dev-tools`
2. Consultez les logs dans la console
3. Vérifiez les fichiers de logs sur le système

### Signalement de Problèmes
Incluez dans votre rapport :
- Version de l'application
- Système d'exploitation
- Logs d'erreur
- Étapes pour reproduire le problème

## 🔄 Mise à Jour

### Mise à jour des dépendances
```bash
npm update
```

### Mise à jour de l'application
1. Téléchargez la nouvelle version
2. Sauvegardez votre configuration (`serveur_ip.txt`)
3. Installez la nouvelle version
4. Restaurez votre configuration

---

**🎉 Félicitations ! Votre application Edara Electron est prête à l'emploi !**

Pour toute question ou assistance, consultez le fichier `README.md` ou contactez l'équipe de support.

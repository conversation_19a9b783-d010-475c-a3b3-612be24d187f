/**
 * Script de test pour l'application Electron
 * Lance l'application en mode test pour vérifier le bon fonctionnement
 */

const { app, BrowserWindow } = require('electron');
const path = require('path');
const log = require('electron-log');

// Configuration pour les tests
app.commandLine.appendSwitch('disable-web-security');
app.commandLine.appendSwitch('disable-features', 'VizDisplayCompositor');

let testWindow = null;
let testResults = {
  windowCreation: false,
  htmlLoading: false,
  preloadScript: false,
  errors: []
};

/**
 * Créer une fenêtre de test
 */
async function createTestWindow() {
  try {
    console.log('🧪 Création de la fenêtre de test...');
    
    testWindow = new BrowserWindow({
      width: 800,
      height: 600,
      show: false, // Ne pas afficher la fenêtre en mode test
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        enableRemoteModule: false,
        preload: path.join(__dirname, 'preload.js')
      }
    });

    testResults.windowCreation = true;
    console.log('✅ Fenêtre créée avec succès');

    // Écouter les erreurs
    testWindow.webContents.on('crashed', () => {
      testResults.errors.push('Crash de la fenêtre');
      console.log('❌ Crash de la fenêtre détecté');
    });

    testWindow.webContents.on('unresponsive', () => {
      testResults.errors.push('Fenêtre non responsive');
      console.log('❌ Fenêtre non responsive');
    });

    testWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription) => {
      testResults.errors.push(`Échec de chargement: ${errorDescription}`);
      console.log(`❌ Échec de chargement: ${errorDescription}`);
    });

    // Tester le chargement du HTML
    console.log('📄 Test de chargement de index-final.html...');
    
    await testWindow.loadFile('index-final.html');
    testResults.htmlLoading = true;
    console.log('✅ HTML chargé avec succès');

    // Tester le script preload
    console.log('🔧 Test du script preload...');
    
    const preloadTest = await testWindow.webContents.executeJavaScript(`
      typeof window.electronAPI !== 'undefined'
    `);

    if (preloadTest) {
      testResults.preloadScript = true;
      console.log('✅ Script preload fonctionnel');
    } else {
      testResults.errors.push('Script preload non chargé');
      console.log('❌ Script preload non fonctionnel');
    }

    // Tester les APIs exposées
    console.log('🔌 Test des APIs exposées...');
    
    const apiTest = await testWindow.webContents.executeJavaScript(`
      typeof window.electronAPI.authenticateOdoo === 'function' &&
      typeof window.electronAPI.showSplashScreen === 'function' &&
      typeof window.electronAPI.getAvailableServers === 'function'
    `);

    if (apiTest) {
      console.log('✅ APIs exposées correctement');
    } else {
      testResults.errors.push('APIs non exposées correctement');
      console.log('❌ APIs non exposées correctement');
    }

    // Tester le CSS
    console.log('🎨 Test du chargement CSS...');
    
    const cssTest = await testWindow.webContents.executeJavaScript(`
      const styles = window.getComputedStyle(document.body);
      styles.fontFamily !== '' && styles.backgroundColor !== ''
    `);

    if (cssTest) {
      console.log('✅ CSS chargé correctement');
    } else {
      testResults.errors.push('CSS non chargé');
      console.log('❌ CSS non chargé correctement');
    }

    // Tester les éléments du formulaire
    console.log('📝 Test des éléments du formulaire...');
    
    const formTest = await testWindow.webContents.executeJavaScript(`
      const form = document.querySelector('.edara-login-form');
      const email = document.getElementById('email');
      const password = document.getElementById('password');
      const instance = document.getElementById('instance');
      const button = document.querySelector('.login-button');
      
      !!(form && email && password && instance && button)
    `);

    if (formTest) {
      console.log('✅ Éléments du formulaire présents');
    } else {
      testResults.errors.push('Éléments du formulaire manquants');
      console.log('❌ Éléments du formulaire manquants');
    }

    return true;

  } catch (error) {
    testResults.errors.push(`Erreur lors de la création de la fenêtre: ${error.message}`);
    console.log(`❌ Erreur: ${error.message}`);
    return false;
  }
}

/**
 * Tester le splash screen
 */
async function testSplashScreen() {
  try {
    console.log('💫 Test de l\'écran de chargement...');
    
    const splashWindow = new BrowserWindow({
      width: 400,
      height: 300,
      frame: false,
      show: false,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        preload: path.join(__dirname, 'preload.js')
      }
    });

    await splashWindow.loadFile('splash-final.html');
    
    // Vérifier les éléments du splash
    const splashTest = await splashWindow.webContents.executeJavaScript(`
      const container = document.querySelector('.splash-container');
      const logo = document.getElementById('logo');
      const progressBar = document.getElementById('progressBar');
      
      !!(container && logo && progressBar)
    `);

    splashWindow.close();

    if (splashTest) {
      console.log('✅ Écran de chargement fonctionnel');
      return true;
    } else {
      testResults.errors.push('Éléments du splash screen manquants');
      console.log('❌ Éléments du splash screen manquants');
      return false;
    }

  } catch (error) {
    testResults.errors.push(`Erreur splash screen: ${error.message}`);
    console.log(`❌ Erreur splash screen: ${error.message}`);
    return false;
  }
}

/**
 * Afficher les résultats des tests
 */
function displayResults() {
  console.log('\n📊 Résultats des tests Electron:');
  console.log(`   Création de fenêtre: ${testResults.windowCreation ? '✅' : '❌'}`);
  console.log(`   Chargement HTML: ${testResults.htmlLoading ? '✅' : '❌'}`);
  console.log(`   Script preload: ${testResults.preloadScript ? '✅' : '❌'}`);
  
  if (testResults.errors.length > 0) {
    console.log('\n❌ Erreurs détectées:');
    testResults.errors.forEach(error => {
      console.log(`   - ${error}`);
    });
  }

  const success = testResults.windowCreation && 
                  testResults.htmlLoading && 
                  testResults.preloadScript && 
                  testResults.errors.length === 0;

  if (success) {
    console.log('\n✅ Tous les tests Electron sont passés avec succès !');
    console.log('🚀 L\'application Electron est prête à être utilisée.');
  } else {
    console.log('\n❌ Certains tests Electron ont échoué.');
    console.log('🔧 Veuillez corriger les erreurs avant de continuer.');
  }

  return success;
}

/**
 * Exécuter tous les tests
 */
async function runElectronTests() {
  try {
    console.log('🚀 Démarrage des tests Electron...\n');

    // Test de création de fenêtre et chargement
    const windowTest = await createTestWindow();
    
    // Test du splash screen
    const splashTest = await testSplashScreen();

    // Afficher les résultats
    const success = displayResults();

    // Nettoyer
    if (testWindow && !testWindow.isDestroyed()) {
      testWindow.close();
    }

    // Quitter l'application
    setTimeout(() => {
      app.quit();
      process.exit(success ? 0 : 1);
    }, 1000);

  } catch (error) {
    console.error('💥 Erreur lors des tests Electron:', error);
    app.quit();
    process.exit(1);
  }
}

// Événements de l'application
app.whenReady().then(runElectronTests);

app.on('window-all-closed', () => {
  app.quit();
});

// Gestion des erreurs
process.on('uncaughtException', (error) => {
  console.error('❌ Erreur non capturée:', error);
  app.quit();
  process.exit(1);
});

process.on('unhandledRejection', (reason) => {
  console.error('❌ Promesse rejetée:', reason);
  app.quit();
  process.exit(1);
});

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edara Workspace</title>
    <style>
        /* Variables CSS pour le thème sombre (par défaut) */
        :root {
            --background-color: #181818;
            --text-color: #F8F9FA;
            --secondary-text-color: #DEE2E6;
            --progress-bar-background: #333333;
            --progress-bar-fill: #FFFFFF;
            --logo-shadow: rgba(0, 0, 0, 0.3);
        }

        /* Variables CSS pour le thème clair */
        @media (prefers-color-scheme: light) {
            :root {
                --background-color: #F5F5F5;
                --text-color: #212529;
                --secondary-text-color: #6C757D;
                --progress-bar-background: #DDDDDD;
                --progress-bar-fill: #212529;
                --logo-shadow: rgba(0, 0, 0, 0.1);
            }
        }

        /* Classes pour le basculement manuel du thème */
        .light-theme {
            --background-color: #F5F5F5;
            --text-color: #212529;
            --secondary-text-color: #6C757D;
            --progress-bar-background: #DDDDDD;
            --progress-bar-fill: #212529;
            --logo-shadow: rgba(0, 0, 0, 0.1);
        }

        .dark-theme {
            --background-color: #181818;
            --text-color: #F8F9FA;
            --secondary-text-color: #DEE2E6;
            --progress-bar-background: #333333;
            --progress-bar-fill: #FFFFFF;
            --logo-shadow: rgba(0, 0, 0, 0.3);
        }

        /* Styles pour la page de splash */
        body {
            margin: 0;
            padding: 0;
            background-color: var(--background-color);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            overflow: hidden;
            color: var(--text-color);
            transition: background-color 0.3s ease, color 0.3s ease;
        }

        .splash-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
        }

        .logo-container {
            margin-bottom: 20px;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .logo {
            width: 70px;
            height: 70px;
            transform: scale(0.6);
            opacity: 0.1;
            transition: all 1.5s cubic-bezier(0.34, 1.56, 0.64, 1);
        }

        .app-title {
            font-size: 18px;
            font-weight: 500;
            margin-bottom: 25px;
            color: var(--text-color);
            opacity: 0;
            transform: translateY(10px);
            transition: opacity 0.5s ease, transform 0.5s ease, color 0.3s ease;
        }

        .progress-bar {
            width: 120px;
            height: 2px;
            background-color: var(--progress-bar-background);
            border-radius: 1px;
            overflow: hidden;
            margin-top: 5px;
            opacity: 0;
            transform: scaleX(0.9);
            transition: opacity 0.5s ease, transform 0.5s ease, background-color 0.3s ease;
        }

        .progress-fill {
            height: 100%;
            width: 0;
            background-color: var(--progress-bar-fill);
            transition: width 0.3s ease-out, background-color 0.3s ease;
        }

        /* Animation de pulsation pour le logo */
        @keyframes pulse {
            0% {
                transform: scale(1);
                filter: drop-shadow(0 4px 8px var(--logo-shadow));
            }
            50% {
                transform: scale(1.08);
                filter: drop-shadow(0 8px 16px var(--logo-shadow));
            }
            100% {
                transform: scale(1);
                filter: drop-shadow(0 4px 8px var(--logo-shadow));
            }
        }

        /* Animation finale */
        @keyframes final-animation {
            0% { transform: scale(1); opacity: 1; }
            20% { transform: scale(1.15); opacity: 1; }
            40% { transform: scale(0.95); opacity: 1; }
            60% { transform: scale(1.05); opacity: 1; }
            80% { transform: scale(0.98); opacity: 1; }
            100% { transform: scale(1); opacity: 1; }
        }

        .logo.pulse {
            animation: pulse 2s infinite ease-in-out;
        }

        .logo.final-animation {
            animation: final-animation 1s ease-in-out forwards;
        }

        /* Animation de brillance pour la barre de progression */
        .progress-bar {
            position: relative;
        }

        .progress-bar::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 2px;
            background: linear-gradient(to right, transparent, var(--text-color, rgba(255, 255, 255, 0.5)), transparent);
            animation: progress-shine 1.5s infinite;
            transform: translateX(-100%);
            opacity: 0.5;
            transition: background 0.3s ease;
        }

        @keyframes progress-shine {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }
    </style>
</head>
<body>
    <div class="splash-container">
        <!-- Logo -->
        <div class="logo-container">
            <img src="img/logo-edara-claire.png" alt="Edara Logo" id="logo" class="logo">
        </div>

        <!-- Titre -->
        <div class="app-title" id="appTitle">Edara Workspace</div>

        <!-- Message de statut -->
        <div class="status-message" id="statusMessage" style="font-size: 12px; color: var(--secondary-text-color); margin-bottom: 15px; opacity: 0; transition: opacity 0.5s ease;">
            Connexion en cours...
        </div>

        <!-- Barre de progression -->
        <div class="progress-bar" id="progressBar">
            <div class="progress-fill" id="progressFill"></div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Éléments DOM
            const logo = document.getElementById('logo');
            const appTitle = document.getElementById('appTitle');
            const statusMessage = document.getElementById('statusMessage');
            const progressBar = document.getElementById('progressBar');
            const progressFill = document.getElementById('progressFill');

            // Fonction pour détecter le thème du système
            function detectColorScheme() {
                const isDarkMode = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;

                // Mettre à jour le logo en fonction du thème
                if (logo) {
                    logo.src = isDarkMode
                        ? 'img/logo-edara-claire.png'
                        : 'img/logo-edara-noire.png';
                }
            }

            // Détecter le thème initial
            detectColorScheme();

            // Écouter les changements de thème
            window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', function(e) {
                detectColorScheme();
            });

            // Séquence d'animation
            setTimeout(() => {
                // Afficher le titre avec un délai
                setTimeout(() => {
                    appTitle.style.opacity = '1';
                    appTitle.style.transform = 'translateY(0)';

                    // Afficher le message de statut
                    setTimeout(() => {
                        statusMessage.style.opacity = '1';

                        // Afficher la barre de progression avec un délai
                        setTimeout(() => {
                            progressBar.style.opacity = '1';
                            progressBar.style.transform = 'scaleX(1)';

                            // Initialiser la progression à 5%
                            updateProgressBar(5);

                            // Simuler une progression
                            simulateProgress();
                        }, 200);
                    }, 100);
                }, 300);
            }, 300);

            // Fonction pour mettre à jour la barre de progression
            function updateProgressBar(percent) {
                // Mettre à jour la largeur de la barre de progression
                progressFill.style.width = `${percent}%`;

                // Faire grandir progressivement le logo en fonction de la progression
                // Calculer la taille et l'opacité en fonction de la progression
                const scale = 0.6 + (percent / 100) * 0.4; // De 0.6 à 1

                // Assurer que l'opacité atteint exactement 1 à la fin (100%)
                let opacity;
                if (percent >= 100) {
                    opacity = 1;
                } else {
                    opacity = 0.1 + (percent / 100) * 0.9; // De 0.1 à 1
                }

                // Appliquer les styles directement
                logo.style.transform = `scale(${scale})`;
                logo.style.opacity = opacity;
            }

            // Fonction pour simuler une progression
            function simulateProgress() {
                let progress = 5;
                const totalDuration = 2000; // 2 secondes au total (plus rapide)
                const steps = 19; // Nombre d'étapes
                const interval = totalDuration / steps;

                // Mettre à jour la progression initiale
                updateProgressBar(progress);

                // Démarrer l'intervalle de progression
                const progressInterval = setInterval(() => {
                    progress += 5;
                    updateProgressBar(progress);

                    // Quand on atteint 95%, lancer l'animation finale
                    if (progress >= 95) {
                        clearInterval(progressInterval);

                        // Remplir complètement la barre de progression
                        updateProgressBar(100);

                        // Lancer l'animation finale du logo
                        // Retirer le style de transformation mais garder l'opacité à 1
                        logo.style.transform = '';
                        logo.style.opacity = '1';

                        // Ajouter la classe pour l'animation finale
                        logo.classList.add('final-animation');

                        // Après l'animation finale, ajouter l'animation de pulsation
                        setTimeout(() => {
                            logo.classList.remove('final-animation');
                            logo.classList.add('pulse');
                        }, 1000);
                    }
                }, interval);
            }
        });
    </script>
</body>
</html>

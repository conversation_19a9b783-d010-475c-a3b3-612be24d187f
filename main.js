/**
 * Point d'entrée principal de l'application Electron Edara
 * Gère l'initialisation de l'application et la création des fenêtres
 */

const { app, BrowserWindow, ipcMain, dialog } = require('electron');
const path = require('path');
const log = require('electron-log');

// Importer les modules personnalisés
const ConnectionManager = require('./src/main/connection-manager');
const OdooAuth = require('./src/main/odoo-auth');
const WindowManager = require('./src/main/window-manager');

// Configuration de electron-log
log.transports.file.level = 'info';
log.transports.console.level = 'debug';
log.transports.console.format = '[{level}] {text}';

// Variables globales
let connectionManager;
let odooAuth;
let windowManager;

/**
 * Configurer les arguments Electron pour optimiser Odoo
 */
function setupElectronArgs() {
  // Arguments pour améliorer la compatibilité avec Odoo
  app.commandLine.appendSwitch('disable-web-security');
  app.commandLine.appendSwitch('disable-features', 'VizDisplayCompositor');
  app.commandLine.appendSwitch('disable-site-isolation-trials');
  app.commandLine.appendSwitch('disable-background-timer-throttling');
  app.commandLine.appendSwitch('disable-backgrounding-occluded-windows');
  app.commandLine.appendSwitch('disable-renderer-backgrounding');
  app.commandLine.appendSwitch('ignore-certificate-errors');
  app.commandLine.appendSwitch('ignore-ssl-errors');
  app.commandLine.appendSwitch('ignore-certificate-errors-spki-list');
  app.commandLine.appendSwitch('allow-running-insecure-content');
  app.commandLine.appendSwitch('disable-http-cache');

  log.info('Arguments Electron configurés pour Odoo');
}

/**
 * Initialisation de l'application
 */
async function initializeApp() {
  try {
    log.info('Initialisation de l\'application Edara...');

    // Configurer les arguments Electron
    setupElectronArgs();

    // Initialiser les gestionnaires
    connectionManager = new ConnectionManager();
    odooAuth = new OdooAuth();
    windowManager = new WindowManager();

    // Charger la configuration des serveurs
    await connectionManager.loadServerConfig();

    log.info('Application initialisée avec succès');
  } catch (error) {
    log.error('Erreur lors de l\'initialisation de l\'application:', error);

    // Afficher une boîte de dialogue d'erreur
    dialog.showErrorBox(
      'Erreur d\'initialisation',
      `Impossible d'initialiser l'application: ${error.message}`
    );

    app.quit();
  }
}

/**
 * Créer la fenêtre principale de connexion
 */
async function createMainWindow() {
  try {
    log.info('Création de la fenêtre principale...');

    const mainWindow = await windowManager.createLoginWindow();

    // Charger l'interface de connexion
    await mainWindow.loadFile('index-final.html');

    log.info('Fenêtre principale créée avec succès');
    return mainWindow;
  } catch (error) {
    log.error('Erreur lors de la création de la fenêtre principale:', error);
    throw error;
  }
}

// Gestionnaires d'événements IPC

/**
 * Gestionnaire pour l'authentification Odoo
 */
ipcMain.handle('authenticate-odoo', async (event, credentials) => {
  try {
    log.info(`Tentative d'authentification pour l'utilisateur: ${credentials.email}`);

    // Déterminer l'URL du serveur selon le type de connexion
    let serverUrl;
    if (credentials.instance === 'local') {
      serverUrl = await connectionManager.findAvailableLocalServer();
      if (!serverUrl) {
        throw new Error('Aucun serveur local disponible');
      }
    } else {
      serverUrl = connectionManager.getRemoteServerUrl();
    }

    log.info(`Connexion vers: ${serverUrl}`);

    // Effectuer l'authentification
    const authResult = await odooAuth.authenticate(
      credentials.email,
      credentials.password,
      serverUrl
    );

    if (authResult.success) {
      log.info(`Authentification réussie pour l'utilisateur: ${authResult.name}`);

      // Afficher l'écran de chargement
      await windowManager.showSplashScreen();

      // Préparer les données pour l'interface Odoo
      const odooData = {
        serverUrl: serverUrl,
        sessionId: authResult.sessionId,
        userId: authResult.userId,
        userName: authResult.name,
        dbName: authResult.dbName || 'ligne-digitale'
      };

      return {
        success: true,
        data: odooData
      };
    } else {
      log.error(`Échec de l'authentification: ${authResult.error}`);
      return {
        success: false,
        error: authResult.error
      };
    }
  } catch (error) {
    log.error('Erreur lors de l\'authentification:', error);
    return {
      success: false,
      error: error.message || 'Erreur inconnue lors de l\'authentification'
    };
  }
});

/**
 * Gestionnaire pour afficher l'écran de chargement
 */
ipcMain.handle('show-splash-screen', async () => {
  try {
    await windowManager.showSplashScreen();
    return { success: true };
  } catch (error) {
    log.error('Erreur lors de l\'affichage de l\'écran de chargement:', error);
    return { success: false, error: error.message };
  }
});

/**
 * Gestionnaire pour masquer l'écran de chargement
 */
ipcMain.handle('hide-splash-screen', async () => {
  try {
    await windowManager.hideSplashScreen();
    return { success: true };
  } catch (error) {
    log.error('Erreur lors du masquage de l\'écran de chargement:', error);
    return { success: false, error: error.message };
  }
});

/**
 * Gestionnaire pour afficher l'interface Odoo
 */
ipcMain.handle('show-odoo-interface', async (event, odooData) => {
  try {
    log.info('Chargement de l\'interface Odoo...');

    const odooWindow = await windowManager.createOdooWindow(odooData);

    // Masquer l'écran de chargement une fois Odoo prêt
    odooWindow.webContents.once('did-finish-load', async () => {
      log.info('Interface Odoo chargée, masquage de l\'écran de chargement');
      await windowManager.hideSplashScreen();
      await windowManager.hideLoginWindow();
    });

    return { success: true };
  } catch (error) {
    log.error('Erreur lors du chargement de l\'interface Odoo:', error);
    return { success: false, error: error.message };
  }
});

/**
 * Gestionnaire pour tester la connexion
 */
ipcMain.handle('test-connection', async (event, serverUrl) => {
  try {
    const isAvailable = await connectionManager.testConnection(serverUrl);
    return { success: true, available: isAvailable };
  } catch (error) {
    log.error(`Erreur lors du test de connexion vers ${serverUrl}:`, error);
    return { success: false, error: error.message };
  }
});

/**
 * Gestionnaire pour obtenir la liste des serveurs disponibles
 */
ipcMain.handle('get-available-servers', async () => {
  try {
    const servers = await connectionManager.getAvailableServers();
    return { success: true, servers };
  } catch (error) {
    log.error('Erreur lors de la récupération des serveurs:', error);
    return { success: false, error: error.message };
  }
});

// Gestionnaires d'utilitaires
ipcMain.handle('get-app-version', () => app.getVersion());
ipcMain.handle('close-app', () => app.quit());
ipcMain.handle('minimize-app', () => BrowserWindow.getFocusedWindow()?.minimize());
ipcMain.handle('maximize-app', () => {
  const window = BrowserWindow.getFocusedWindow();
  if (window?.isMaximized()) {
    window.unmaximize();
  } else {
    window?.maximize();
  }
});

// Événements de l'application

/**
 * Événement déclenché quand Electron est prêt
 */
app.whenReady().then(async () => {
  await initializeApp();
  await createMainWindow();

  // Sur macOS, recréer une fenêtre quand l'icône du dock est cliquée
  app.on('activate', async () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      await createMainWindow();
    }
  });
});

/**
 * Quitter quand toutes les fenêtres sont fermées
 */
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

/**
 * Événement avant la fermeture de l'application
 */
app.on('before-quit', () => {
  log.info('Fermeture de l\'application Edara');
});

// Gestion des erreurs non capturées
process.on('uncaughtException', (error) => {
  log.error('Erreur non capturée:', error);
});

process.on('unhandledRejection', (reason, promise) => {
  log.error('Promesse rejetée non gérée:', reason);
});

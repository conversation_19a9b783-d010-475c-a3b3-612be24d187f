#!/usr/bin/env node

/**
 * Script de démarrage optimisé pour l'application Edara Electron
 * Avec paramètres spéciaux pour améliorer la compatibilité Odoo
 */

const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 Démarrage optimisé de l\'application Edara Electron...');

// Arguments Electron optimisés pour Odoo
const electronArgs = [
  '.',
  '--disable-web-security',
  '--disable-features=VizDisplayCompositor',
  '--disable-site-isolation-trials',
  '--disable-background-timer-throttling',
  '--disable-backgrounding-occluded-windows',
  '--disable-renderer-backgrounding',
  '--ignore-certificate-errors',
  '--ignore-ssl-errors',
  '--ignore-certificate-errors-spki-list',
  '--allow-running-insecure-content',
  '--disable-http-cache',
  '--no-sandbox',
  '--disable-dev-shm-usage'
];

// Variables d'environnement optimisées
const env = {
  ...process.env,
  ELECTRON_DISABLE_SECURITY_WARNINGS: 'true',
  ELECTRON_ENABLE_LOGGING: 'true'
};

console.log('🔧 Arguments Electron:', electronArgs.slice(1).join(' '));

// Lancer Electron
const electronPath = path.join(__dirname, 'node_modules', '.bin', 'electron');
const electronProcess = spawn(electronPath, electronArgs, {
  env: env,
  stdio: 'inherit',
  cwd: __dirname
});

// Gestion des événements
electronProcess.on('close', (code) => {
  if (code === 0) {
    console.log('✅ Application fermée normalement.');
  } else {
    console.error(`❌ Application fermée avec le code d'erreur: ${code}`);
  }
  process.exit(code);
});

electronProcess.on('error', (error) => {
  console.error('❌ Erreur lors du lancement de l\'application:', error);
  process.exit(1);
});

// Gestion des signaux
process.on('SIGINT', () => {
  console.log('\n🛑 Arrêt de l\'application...');
  electronProcess.kill('SIGINT');
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Arrêt de l\'application...');
  electronProcess.kill('SIGTERM');
});

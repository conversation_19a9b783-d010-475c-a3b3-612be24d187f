/**
 * Gestionnaire de fenêtres pour l'application Edara
 * Gère la création et la gestion des différentes fenêtres de l'application
 */

const { BrowserWindow, session } = require('electron');
const path = require('path');
const log = require('electron-log');

class WindowManager {
  constructor() {
    this.mainWindow = null; // Une seule fenêtre pour tout
    this.currentView = 'login'; // 'login', 'splash', 'odoo'
  }

  /**
   * Créer la fenêtre principale unique
   * @returns {Promise<BrowserWindow>} - Fenêtre principale
   */
  async createMainWindow() {
    try {
      log.info('Création de la fenêtre principale...');

      this.mainWindow = new BrowserWindow({
        width: 1200,
        height: 800,
        minWidth: 900,
        minHeight: 600,
        show: false,
        backgroundColor: '#181818',
        titleBarStyle: 'default',
        webPreferences: {
          nodeIntegration: false,
          contextIsolation: true,
          enableRemoteModule: false,
          webSecurity: false, // Nécessaire pour Odoo
          allowRunningInsecureContent: true,
          experimentalFeatures: true,
          plugins: true,
          webgl: true,
          webaudio: true,
          preload: path.join(__dirname, '../../preload.js')
        }
      });

      // Événements de la fenêtre
      this.mainWindow.once('ready-to-show', () => {
        this.mainWindow.show();
        log.info('Fenêtre principale affichée');
      });

      this.mainWindow.on('closed', () => {
        this.mainWindow = null;
        log.info('Fenêtre principale fermée');
      });

      // Désactiver le menu par défaut
      this.mainWindow.setMenu(null);

      // Charger l'écran de connexion initial
      await this.showLoginView();

      return this.mainWindow;

    } catch (error) {
      log.error('Erreur lors de la création de la fenêtre principale:', error);
      throw error;
    }
  }

  /**
   * Afficher la vue de connexion
   * @returns {Promise<void>}
   */
  async showLoginView() {
    try {
      log.info('Affichage de la vue de connexion...');

      if (!this.mainWindow || this.mainWindow.isDestroyed()) {
        throw new Error('Fenêtre principale non disponible');
      }

      this.currentView = 'login';
      await this.mainWindow.loadFile('index-final.html');

      log.info('Vue de connexion affichée');
    } catch (error) {
      log.error('Erreur lors de l\'affichage de la vue de connexion:', error);
      throw error;
    }
  }

  /**
   * Afficher la vue de chargement (splash)
   * @returns {Promise<void>}
   */
  async showSplashView() {
    try {
      log.info('Affichage de la vue de chargement...');

      if (!this.mainWindow || this.mainWindow.isDestroyed()) {
        throw new Error('Fenêtre principale non disponible');
      }

      this.currentView = 'splash';
      await this.mainWindow.loadFile('splash-final.html');

      log.info('Vue de chargement affichée');
    } catch (error) {
      log.error('Erreur lors de l\'affichage de la vue de chargement:', error);
      throw error;
    }
  }

  /**
   * Afficher la vue Odoo
   * @param {Object} odooData - Données de connexion Odoo
   * @returns {Promise<void>}
   */
  async showOdooView(odooData) {
    try {
      log.info('Affichage de la vue Odoo...');

      if (!this.mainWindow || this.mainWindow.isDestroyed()) {
        throw new Error('Fenêtre principale non disponible');
      }

      this.currentView = 'odoo';

      // Configurer les événements pour Odoo
      this.setupOdooEventHandlers();

      // Configurer la session pour Odoo
      try {
        await this.setupOdooSession(odooData);
      } catch (sessionError) {
        log.warn('Erreur lors de la configuration de session, continuation sans cookie:', sessionError.message);
      }

      // Construire l'URL Odoo avec session authentifiée
      const odooUrl = `${odooData.serverUrl}/web?db=${odooData.dbName}`;
      log.info(`Chargement de l'interface Odoo avec session: ${odooUrl}`);

      // Charger l'interface Odoo avec la session - Approche simplifiée
      try {
        // Méthode directe : charger avec injection de session
        await this.loadOdooDirectly(odooData);
      } catch (loadError) {
        log.error('Erreur lors du chargement direct:', loadError.message);

        // Fallback : chargement simple et injection après
        log.info('Tentative de fallback avec injection post-chargement...');
        const simpleUrl = `${odooData.serverUrl}/web?db=${odooData.dbName}`;
        await this.mainWindow.loadURL(simpleUrl, {
          userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        });

        // Attendre le chargement puis injecter la session
        await new Promise(resolve => setTimeout(resolve, 3000));
        await this.injectSessionAfterLoad(odooData);
      }

      log.info('Vue Odoo affichée');
    } catch (error) {
      log.error('Erreur lors de l\'affichage de la vue Odoo:', error);
      throw error;
    }
  }

  /**
   * Charger Odoo directement avec injection de session
   * @param {Object} odooData - Données de session
   * @returns {Promise<void>}
   */
  async loadOdooDirectly(odooData) {
    try {
      log.info('Chargement direct d\'Odoo avec injection de session...');

      // Étape 1: Charger la page de base d'Odoo
      const baseUrl = `${odooData.serverUrl}/web`;
      await this.mainWindow.loadURL(baseUrl, {
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      });

      // Étape 2: Attendre que la page soit chargée
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Étape 3: Injecter la session et rediriger
      await this.mainWindow.webContents.executeJavaScript(`
        // Fonction pour définir le cookie et rediriger
        (function() {
          try {
            // Définir le cookie de session
            const domain = '${new URL(odooData.serverUrl).hostname}';
            document.cookie = 'session_id=${odooData.sessionId}; path=/; domain=' + domain;

            // Définir d'autres cookies utiles
            document.cookie = 'frontend_lang=fr_FR; path=/; domain=' + domain;

            // Rediriger vers l'interface avec la base de données
            console.log('Redirection vers l\\'interface Odoo avec session...');
            window.location.href = '${odooData.serverUrl}/web?db=${odooData.dbName}';

            return true;
          } catch (e) {
            console.error('Erreur lors de l\\'injection de session:', e);
            return false;
          }
        })();
      `);

      // Étape 4: Attendre la redirection et vérifier
      await new Promise(resolve => setTimeout(resolve, 4000));

      // Étape 5: Vérifier l'état de connexion
      const isConnected = await this.checkOdooConnectionStatus();
      if (!isConnected) {
        throw new Error('Échec de la connexion directe - session non reconnue');
      }

      log.info('Chargement direct d\'Odoo réussi');
    } catch (error) {
      log.error('Erreur lors du chargement direct:', error.message);
      throw error;
    }
  }

  /**
   * Injecter la session après chargement de la page
   * @param {Object} odooData - Données de session
   * @returns {Promise<void>}
   */
  async injectSessionAfterLoad(odooData) {
    try {
      log.info('Injection de session après chargement...');

      // Injecter la session et forcer la reconnexion
      await this.mainWindow.webContents.executeJavaScript(`
        (function() {
          try {
            // Définir le cookie de session
            const domain = '${new URL(odooData.serverUrl).hostname}';
            document.cookie = 'session_id=${odooData.sessionId}; path=/; domain=' + domain;
            document.cookie = 'frontend_lang=fr_FR; path=/; domain=' + domain;

            // Si on est sur la page de login, essayer de contourner
            if (window.location.href.includes('/web/login') || document.querySelector('.oe_login_form')) {
              console.log('Page de login détectée, tentative de contournement...');

              // Essayer de rediriger vers l'interface directement
              setTimeout(() => {
                window.location.href = '${odooData.serverUrl}/web?db=${odooData.dbName}';
              }, 1000);
            } else {
              // Recharger la page pour appliquer la session
              console.log('Rechargement de la page pour appliquer la session...');
              window.location.reload();
            }

            return true;
          } catch (e) {
            console.error('Erreur lors de l\\'injection post-chargement:', e);
            return false;
          }
        })();
      `);

      // Attendre l'effet de l'injection
      await new Promise(resolve => setTimeout(resolve, 3000));

      log.info('Injection de session post-chargement terminée');
    } catch (error) {
      log.error('Erreur lors de l\'injection post-chargement:', error.message);
      throw error;
    }
  }

  /**
   * Charger Odoo avec la session authentifiée (ancienne méthode)
   * @param {string} odooUrl - URL d'Odoo
   * @param {Object} odooData - Données de session
   * @returns {Promise<void>}
   */
  async loadOdooWithSession(odooUrl, odooData) {
    try {
      log.info('Chargement d\'Odoo avec session authentifiée...');

      // Charger l'URL avec les headers appropriés
      await this.mainWindow.loadURL(odooUrl, {
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        extraHeaders: `Cookie: session_id=${odooData.sessionId}`
      });

      // Attendre que la page soit chargée
      await new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('Timeout lors du chargement d\'Odoo'));
        }, 15000);

        this.mainWindow.webContents.once('did-finish-load', () => {
          clearTimeout(timeout);
          resolve();
        });

        this.mainWindow.webContents.once('did-fail-load', (event, errorCode, errorDescription) => {
          clearTimeout(timeout);
          reject(new Error(`Échec de chargement: ${errorDescription}`));
        });
      });

      // Attendre un peu pour que la page se stabilise
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Vérifier si on est bien connecté
      const isConnected = await this.checkOdooConnectionStatus();
      if (!isConnected) {
        throw new Error('Session non reconnue par Odoo - utilisateur non connecté');
      }

      log.info('Odoo chargé avec succès avec la session authentifiée');
    } catch (error) {
      log.error('Erreur lors du chargement avec session:', error.message);
      throw error;
    }
  }

  /**
   * Forcer la connexion Odoo en injectant la session
   * @param {Object} odooData - Données de session
   * @returns {Promise<void>}
   */
  async forceOdooLogin(odooData) {
    try {
      log.info('Tentative de connexion forcée à Odoo...');

      // Charger d'abord la page de base
      const baseUrl = `${odooData.serverUrl}/web`;
      await this.mainWindow.loadURL(baseUrl, {
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      });

      // Attendre que la page soit chargée
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Injecter la session via JavaScript
      await this.mainWindow.webContents.executeJavaScript(`
        // Définir le cookie de session
        document.cookie = 'session_id=${odooData.sessionId}; path=/; domain=${new URL(odooData.serverUrl).hostname}';

        // Recharger la page pour appliquer la session
        setTimeout(() => {
          window.location.href = '${odooData.serverUrl}/web?db=${odooData.dbName}';
        }, 500);
      `);

      // Attendre le rechargement
      await new Promise(resolve => setTimeout(resolve, 3000));

      // Vérifier si la connexion forcée a fonctionné
      const isConnected = await this.checkOdooConnectionStatus();
      if (!isConnected) {
        throw new Error('La connexion forcée n\'a pas réussi - utilisateur toujours non connecté');
      }

      log.info('Connexion forcée réussie - utilisateur connecté');
    } catch (error) {
      log.error('Erreur lors de la connexion forcée:', error.message);
      throw error;
    }
  }

  /**
   * Processus complet de connexion avec transitions automatiques
   * @param {Object} odooData - Données de connexion Odoo
   * @returns {Promise<void>}
   */
  async processCompleteLogin(odooData) {
    try {
      log.info('Démarrage du processus de connexion complet...');

      // 1. Afficher l'écran de chargement
      await this.showSplashView();

      // 2. Attendre un peu pour l'expérience utilisateur
      await new Promise(resolve => setTimeout(resolve, 1500));

      // 3. Charger l'interface Odoo
      await this.showOdooView(odooData);

      log.info('Processus de connexion complet terminé');
    } catch (error) {
      log.error('Erreur lors du processus de connexion:', error);
      throw error;
    }
  }

  /**
   * Méthode de compatibilité - redirige vers showSplashView()
   * @deprecated Utiliser showSplashView() à la place
   */
  async showSplashScreen() {
    return await this.showSplashView();
  }

  /**
   * Masquer l'écran de chargement (méthode de compatibilité)
   * @returns {Promise<void>}
   */
  async hideSplashScreen() {
    // Dans le mode fenêtre unique, pas besoin de masquer
    // La transition se fait automatiquement lors du changement de vue
    log.info('Transition automatique - pas de masquage nécessaire');
  }

  /**
   * Masquer la fenêtre de connexion (méthode de compatibilité)
   * @returns {Promise<void>}
   */
  async hideLoginWindow() {
    // Dans le mode fenêtre unique, pas besoin de masquer
    // La transition se fait automatiquement lors du changement de vue
    log.info('Transition automatique - pas de masquage nécessaire');
  }

  /**
   * Créer la fenêtre Odoo (méthode de compatibilité)
   * @param {Object} odooData - Données de connexion Odoo
   * @returns {Promise<BrowserWindow>} - Fenêtre principale
   */
  async createOdooWindow(odooData) {
    // Dans le mode fenêtre unique, on change juste la vue
    await this.showOdooView(odooData);
    return this.mainWindow;
  }

  /**
   * Ancienne méthode createOdooWindow (conservée pour référence)
   * @param {Object} odooData - Données de connexion Odoo
   * @returns {Promise<BrowserWindow>} - Fenêtre Odoo
   */
  async createOdooWindowOld(odooData) {
    try {
      log.info('Création de la fenêtre Odoo...');

      // Fermer la fenêtre Odoo existante s'il y en a une
      if (this.odooWindow && !this.odooWindow.isDestroyed()) {
        this.odooWindow.close();
      }

      this.odooWindow = new BrowserWindow({
        width: 1400,
        height: 900,
        minWidth: 1000,
        minHeight: 600,
        show: false,
        backgroundColor: '#181818',
        titleBarStyle: 'default',
        webPreferences: {
          nodeIntegration: false,
          contextIsolation: false, // Désactivé pour Odoo
          enableRemoteModule: false,
          webSecurity: false, // Nécessaire pour Odoo
          allowRunningInsecureContent: true,
          experimentalFeatures: true,
          plugins: true,
          webgl: true,
          webaudio: true,
          additionalArguments: ['--disable-web-security', '--disable-features=VizDisplayCompositor'],
          preload: path.join(__dirname, '../../preload.js')
        }
      });

      // Configurer les événements avant le chargement
      this.setupOdooEventHandlers();

      // Construire l'URL Odoo avec paramètres optimisés
      const odooUrl = `${odooData.serverUrl}/web?db=${odooData.dbName}`;

      log.info(`Chargement de l'interface Odoo: ${odooUrl}`);

      // Configurer la session pour Odoo (après la création de la fenêtre)
      try {
        await this.setupOdooSession(odooData);
      } catch (sessionError) {
        log.warn('Erreur lors de la configuration de session, continuation sans cookie:', sessionError.message);
      }

      // Charger l'interface Odoo avec options
      try {
        await this.odooWindow.loadURL(odooUrl, {
          userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        });
      } catch (loadError) {
        log.error('Erreur lors du chargement initial d\'Odoo:', loadError.message);

        // Tentative de chargement alternatif avec URL simplifiée
        const simpleUrl = `${odooData.serverUrl}/web/login?db=${odooData.dbName}`;
        log.info(`Tentative de chargement alternatif: ${simpleUrl}`);

        await this.odooWindow.loadURL(simpleUrl, {
          userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        });
      }

      // Événements de la fenêtre
      this.odooWindow.once('ready-to-show', () => {
        this.odooWindow.show();
        this.odooWindow.maximize();
        log.info('Interface Odoo affichée');
      });

      this.odooWindow.on('closed', () => {
        this.odooWindow = null;
        log.info('Fenêtre Odoo fermée');

        // Réafficher la fenêtre de connexion si elle existe
        if (this.loginWindow && !this.loginWindow.isDestroyed()) {
          this.loginWindow.show();
        }
      });

      // Surveiller le chargement de la page
      this.odooWindow.webContents.on('did-finish-load', () => {
        log.info('Interface Odoo entièrement chargée');

        // Vérifier si Odoo est prêt
        this.checkOdooReady();
      });

      this.odooWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription) => {
        log.error(`Échec du chargement de l'interface Odoo: ${errorDescription} (${errorCode})`);
      });

      // Désactiver le menu par défaut
      this.odooWindow.setMenu(null);

      return this.odooWindow;

    } catch (error) {
      log.error('Erreur lors de la création de la fenêtre Odoo:', error);
      throw error;
    }
  }

  /**
   * Configurer la session pour Odoo
   * @param {Object} odooData - Données de connexion Odoo
   * @returns {Promise<void>}
   */
  async setupOdooSession(odooData) {
    try {
      log.info('Configuration de la session Odoo...');

      // Configurer la session avec des paramètres optimisés pour Odoo
      const ses = this.mainWindow.webContents.session;

      // Nettoyer les données existantes
      await ses.clearStorageData({
        storages: ['cookies', 'localstorage', 'sessionstorage', 'cachestorage']
      });

      // Configurer les permissions
      ses.setPermissionRequestHandler((webContents, permission, callback) => {
        // Autoriser toutes les permissions pour Odoo
        callback(true);
      });

      // Configurer les headers pour éviter les problèmes CORS
      ses.webRequest.onBeforeSendHeaders((details, callback) => {
        details.requestHeaders['User-Agent'] = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36';
        details.requestHeaders['Accept'] = 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8';
        details.requestHeaders['Accept-Language'] = 'fr-FR,fr;q=0.9,en;q=0.8';
        details.requestHeaders['Cache-Control'] = 'no-cache';
        callback({ requestHeaders: details.requestHeaders });
      });

      // Extraire le domaine de l'URL
      const url = new URL(odooData.serverUrl);
      const domain = `${url.protocol}//${url.hostname}${url.port ? `:${url.port}` : ''}`;

      // Définir le cookie de session avec gestion d'erreur
      const cookie = {
        url: domain,
        name: 'session_id',
        value: odooData.sessionId,
        path: '/',
        httpOnly: false,
        secure: url.protocol === 'https:'
        // Supprimer sameSite qui peut causer des problèmes
      };

      try {
        await ses.cookies.set(cookie);
        log.info('Cookie de session défini avec succès');
      } catch (cookieError) {
        log.warn('Erreur lors de la définition du cookie, tentative alternative:', cookieError.message);

        // Tentative avec un cookie simplifié
        const simpleCookie = {
          url: domain,
          name: 'session_id',
          value: odooData.sessionId,
          path: '/'
        };

        try {
          await ses.cookies.set(simpleCookie);
          log.info('Cookie de session simplifié défini avec succès');
        } catch (fallbackError) {
          log.warn('Impossible de définir le cookie de session via l\'API:', fallbackError.message);
          // Continuer - on essaiera d'injecter le cookie via JavaScript
        }
      }

      // Définir également des cookies supplémentaires pour Odoo
      try {
        // Cookie pour la base de données
        await ses.cookies.set({
          url: domain,
          name: 'frontend_lang',
          value: 'fr_FR',
          path: '/'
        });

        // Cookie pour l'utilisateur
        if (odooData.userId) {
          await ses.cookies.set({
            url: domain,
            name: 'uid',
            value: odooData.userId.toString(),
            path: '/'
          });
        }
      } catch (extraCookieError) {
        log.warn('Erreur lors de la définition des cookies supplémentaires:', extraCookieError.message);
      }

      // Vérifier que le cookie a été défini
      const cookies = await ses.cookies.get({ url: domain });
      const sessionCookie = cookies.find(c => c.name === 'session_id');

      if (sessionCookie) {
        log.info(`Cookie de session défini avec succès: ${sessionCookie.value.substring(0, 8)}...`);
      } else {
        log.warn('Échec de la définition du cookie de session');
      }

    } catch (error) {
      log.error('Erreur lors de la configuration de la session Odoo:', error);
      throw error;
    }
  }

  /**
   * Configurer les gestionnaires d'événements pour Odoo
   */
  setupOdooEventHandlers() {
    if (!this.mainWindow || this.mainWindow.isDestroyed()) {
      return;
    }

    // Gérer les erreurs de chargement des ressources
    this.mainWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription, validatedURL) => {
      log.error(`Échec de chargement: ${errorDescription} pour ${validatedURL}`);

      // Réessayer le chargement pour certaines erreurs
      if (errorCode === -106 || errorCode === -118) { // ERR_INTERNET_DISCONNECTED ou ERR_CONNECTION_TIMED_OUT
        log.info('Tentative de rechargement...');
        setTimeout(() => {
          if (!this.mainWindow.isDestroyed()) {
            this.mainWindow.reload();
          }
        }, 2000);
      }
    });

    // Gérer les erreurs de certificat
    this.mainWindow.webContents.on('certificate-error', (event, url, error, certificate, callback) => {
      log.warn(`Erreur de certificat pour ${url}: ${error}`);
      // Accepter les certificats auto-signés pour les serveurs locaux
      if (url.includes('192.168.') || url.includes('localhost') || url.includes('127.0.0.1')) {
        event.preventDefault();
        callback(true);
      } else {
        callback(false);
      }
    });

    // Gérer les nouvelles fenêtres (popups Odoo)
    this.mainWindow.webContents.setWindowOpenHandler(({ url }) => {
      log.info(`Ouverture de nouvelle fenêtre: ${url}`);
      return {
        action: 'allow',
        overrideBrowserWindowOptions: {
          webPreferences: {
            nodeIntegration: false,
            contextIsolation: false,
            webSecurity: false,
            allowRunningInsecureContent: true
          }
        }
      };
    });

    // Injecter du JavaScript pour corriger les problèmes Odoo
    this.mainWindow.webContents.on('dom-ready', () => {
      this.injectOdooFixes();
    });
  }

  /**
   * Vérifier l'état de connexion Odoo
   * @returns {Promise<boolean>} - True si connecté, false sinon
   */
  async checkOdooConnectionStatus() {
    try {
      const currentUrl = this.mainWindow.webContents.getURL();

      // Si on est sur la page de login, on n'est pas connecté
      if (currentUrl.includes('/web/login')) {
        log.info('Utilisateur non connecté - sur la page de login');
        return false;
      }

      // Vérifier via JavaScript si l'utilisateur est connecté
      const isConnected = await this.mainWindow.webContents.executeJavaScript(`
        // Vérifier si Odoo est chargé et si l'utilisateur est connecté
        (function() {
          try {
            // Vérifier si on a accès aux données utilisateur Odoo
            if (typeof odoo !== 'undefined' && odoo.session_info) {
              return odoo.session_info.uid && odoo.session_info.uid !== false;
            }

            // Vérifier si on est sur une page authentifiée
            const body = document.body;
            if (body) {
              // Rechercher des éléments qui indiquent qu'on est connecté
              const hasUserMenu = document.querySelector('.o_user_menu, .oe_topbar_name, .o_main_navbar');
              const hasLoginForm = document.querySelector('.oe_login_form, .o_database_list');

              return hasUserMenu && !hasLoginForm;
            }

            return false;
          } catch (e) {
            return false;
          }
        })();
      `);

      log.info(`État de connexion Odoo: ${isConnected ? 'connecté' : 'non connecté'}`);
      return isConnected;
    } catch (error) {
      log.error('Erreur lors de la vérification de l\'état de connexion:', error.message);
      return false;
    }
  }

  /**
   * Injecter des corrections JavaScript pour Odoo
   */
  async injectOdooFixes() {
    try {
      await this.mainWindow.webContents.executeJavaScript(`
        // Corriger les problèmes de chargement des ressources
        (function() {
          // Intercepter les erreurs de chargement
          const originalFetch = window.fetch;
          window.fetch = function(...args) {
            return originalFetch.apply(this, args).catch(error => {
              console.warn('Fetch error intercepted:', error);
              // Réessayer une fois
              return originalFetch.apply(this, args);
            });
          };

          // Corriger les problèmes de Content-Length
          const originalXHR = window.XMLHttpRequest;
          window.XMLHttpRequest = function() {
            const xhr = new originalXHR();
            const originalOpen = xhr.open;
            xhr.open = function(method, url, ...args) {
              // Ajouter des headers pour éviter les problèmes
              const result = originalOpen.apply(this, [method, url, ...args]);
              this.setRequestHeader('Cache-Control', 'no-cache');
              return result;
            };
            return xhr;
          };

          // Attendre que Odoo soit chargé
          let checkOdoo = setInterval(() => {
            if (typeof odoo !== 'undefined' && odoo.define) {
              console.log('Odoo détecté et fonctionnel');
              clearInterval(checkOdoo);
            } else if (document.readyState === 'complete') {
              // Si Odoo n'est pas chargé après 10 secondes, recharger
              setTimeout(() => {
                if (typeof odoo === 'undefined') {
                  console.log('Rechargement de la page - Odoo non détecté');
                  window.location.reload();
                }
              }, 10000);
              clearInterval(checkOdoo);
            }
          }, 500);
        })();
      `);
    } catch (error) {
      log.error('Erreur lors de l\'injection des corrections:', error);
    }
  }

  /**
   * Vérifier si l'interface Odoo est prête
   * @returns {Promise<void>}
   */
  async checkOdooReady() {
    try {
      if (!this.odooWindow || this.odooWindow.isDestroyed()) {
        return;
      }

      // Exécuter du JavaScript dans la page pour vérifier si Odoo est prêt
      const isReady = await this.odooWindow.webContents.executeJavaScript(`
        (function() {
          // Vérifier la présence d'éléments spécifiques à Odoo
          const odooElements = [
            document.querySelector('.o_main_navbar'),
            document.querySelector('.o_action_manager'),
            document.querySelector('#oe_main_menu_navbar')
          ];

          // Vérifier si au moins un élément Odoo est présent
          const hasOdooElements = odooElements.some(el => el !== null);

          // Vérifier si la page n'est pas en cours de chargement
          const isNotLoading = !document.querySelector('.o_loading');

          return hasOdooElements && isNotLoading;
        })();
      `);

      if (isReady) {
        log.info('Interface Odoo prête et fonctionnelle');

        // Masquer l'écran de chargement
        await this.hideSplashScreen();

        // Émettre un événement pour notifier que Odoo est prêt
        if (this.odooWindow && !this.odooWindow.isDestroyed()) {
          this.odooWindow.webContents.send('odoo-ready');
        }
      } else {
        // Réessayer après un délai
        setTimeout(() => this.checkOdooReady(), 1000);
      }

    } catch (error) {
      log.error('Erreur lors de la vérification de l\'état d\'Odoo:', error);

      // En cas d'erreur, masquer l'écran de chargement après un délai
      setTimeout(async () => {
        await this.hideSplashScreen();
      }, 3000);
    }
  }

  /**
   * Obtenir la fenêtre active
   * @returns {BrowserWindow|null} - Fenêtre active ou null
   */
  getActiveWindow() {
    if (this.odooWindow && !this.odooWindow.isDestroyed()) {
      return this.odooWindow;
    } else if (this.loginWindow && !this.loginWindow.isDestroyed()) {
      return this.loginWindow;
    } else if (this.splashWindow && !this.splashWindow.isDestroyed()) {
      return this.splashWindow;
    }
    return null;
  }

  /**
   * Fermer toutes les fenêtres
   * @returns {Promise<void>}
   */
  async closeAllWindows() {
    try {
      log.info('Fermeture de toutes les fenêtres...');

      if (this.odooWindow && !this.odooWindow.isDestroyed()) {
        this.odooWindow.close();
      }

      if (this.splashWindow && !this.splashWindow.isDestroyed()) {
        this.splashWindow.close();
      }

      if (this.loginWindow && !this.loginWindow.isDestroyed()) {
        this.loginWindow.close();
      }

    } catch (error) {
      log.error('Erreur lors de la fermeture des fenêtres:', error);
    }
  }
}

module.exports = WindowManager;

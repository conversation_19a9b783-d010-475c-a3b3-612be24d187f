/**
 * Gestionnaire de fenêtres pour l'application Edara
 * Gère la création et la gestion des différentes fenêtres de l'application
 */

const { BrowserWindow, session } = require('electron');
const path = require('path');
const log = require('electron-log');

class WindowManager {
  constructor() {
    this.loginWindow = null;
    this.splashWindow = null;
    this.odooWindow = null;
  }

  /**
   * Créer la fenêtre de connexion
   * @returns {Promise<BrowserWindow>} - Fenêtre de connexion
   */
  async createLoginWindow() {
    try {
      log.info('Création de la fenêtre de connexion...');

      this.loginWindow = new BrowserWindow({
        width: 900,
        height: 600,
        minWidth: 800,
        minHeight: 500,
        show: false,
        backgroundColor: '#181818',
        titleBarStyle: 'default',
        webPreferences: {
          nodeIntegration: false,
          contextIsolation: true,
          enableRemoteModule: false,
          preload: path.join(__dirname, '../../preload.js')
        }
      });

      // Événements de la fenêtre
      this.loginWindow.once('ready-to-show', () => {
        this.loginWindow.show();
        log.info('Fenêtre de connexion affichée');
      });

      this.loginWindow.on('closed', () => {
        this.loginWindow = null;
        log.info('Fenêtre de connexion fermée');
      });

      // Désactiver le menu par défaut
      this.loginWindow.setMenu(null);

      return this.loginWindow;

    } catch (error) {
      log.error('Erreur lors de la création de la fenêtre de connexion:', error);
      throw error;
    }
  }

  /**
   * Créer et afficher l'écran de chargement
   * @returns {Promise<BrowserWindow>} - Fenêtre de chargement
   */
  async showSplashScreen() {
    try {
      log.info('Affichage de l\'écran de chargement...');

      // Fermer l'écran de chargement existant s'il y en a un
      if (this.splashWindow && !this.splashWindow.isDestroyed()) {
        this.splashWindow.close();
      }

      this.splashWindow = new BrowserWindow({
        width: 400,
        height: 300,
        frame: false,
        alwaysOnTop: true,
        transparent: true,
        resizable: false,
        show: false,
        backgroundColor: '#181818',
        webPreferences: {
          nodeIntegration: false,
          contextIsolation: true,
          enableRemoteModule: false,
          preload: path.join(__dirname, '../../preload.js')
        }
      });

      // Charger l'écran de chargement
      await this.splashWindow.loadFile('splash-final.html');

      // Centrer la fenêtre
      this.splashWindow.center();

      // Afficher la fenêtre
      this.splashWindow.show();

      // Événements de la fenêtre
      this.splashWindow.on('closed', () => {
        this.splashWindow = null;
        log.info('Écran de chargement fermé');
      });

      log.info('Écran de chargement affiché');
      return this.splashWindow;

    } catch (error) {
      log.error('Erreur lors de l\'affichage de l\'écran de chargement:', error);
      throw error;
    }
  }

  /**
   * Masquer l'écran de chargement
   * @returns {Promise<void>}
   */
  async hideSplashScreen() {
    try {
      if (this.splashWindow && !this.splashWindow.isDestroyed()) {
        log.info('Masquage de l\'écran de chargement...');
        this.splashWindow.close();
        this.splashWindow = null;
      }
    } catch (error) {
      log.error('Erreur lors du masquage de l\'écran de chargement:', error);
    }
  }

  /**
   * Masquer la fenêtre de connexion
   * @returns {Promise<void>}
   */
  async hideLoginWindow() {
    try {
      if (this.loginWindow && !this.loginWindow.isDestroyed()) {
        log.info('Masquage de la fenêtre de connexion...');
        this.loginWindow.hide();
      }
    } catch (error) {
      log.error('Erreur lors du masquage de la fenêtre de connexion:', error);
    }
  }

  /**
   * Créer la fenêtre Odoo
   * @param {Object} odooData - Données de connexion Odoo
   * @returns {Promise<BrowserWindow>} - Fenêtre Odoo
   */
  async createOdooWindow(odooData) {
    try {
      log.info('Création de la fenêtre Odoo...');

      // Fermer la fenêtre Odoo existante s'il y en a une
      if (this.odooWindow && !this.odooWindow.isDestroyed()) {
        this.odooWindow.close();
      }

      this.odooWindow = new BrowserWindow({
        width: 1400,
        height: 900,
        minWidth: 1000,
        minHeight: 600,
        show: false,
        backgroundColor: '#181818',
        titleBarStyle: 'default',
        webPreferences: {
          nodeIntegration: false,
          contextIsolation: false, // Désactivé pour Odoo
          enableRemoteModule: false,
          webSecurity: false, // Nécessaire pour Odoo
          allowRunningInsecureContent: true,
          experimentalFeatures: true,
          plugins: true,
          webgl: true,
          webaudio: true,
          additionalArguments: ['--disable-web-security', '--disable-features=VizDisplayCompositor'],
          preload: path.join(__dirname, '../../preload.js')
        }
      });

      // Configurer la session pour Odoo
      await this.setupOdooSession(odooData);

      // Construire l'URL Odoo avec paramètres optimisés
      const odooUrl = `${odooData.serverUrl}/web?db=${odooData.dbName}`;

      log.info(`Chargement de l'interface Odoo: ${odooUrl}`);

      // Configurer les événements avant le chargement
      this.setupOdooEventHandlers();

      // Charger l'interface Odoo avec options
      await this.odooWindow.loadURL(odooUrl, {
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      });

      // Événements de la fenêtre
      this.odooWindow.once('ready-to-show', () => {
        this.odooWindow.show();
        this.odooWindow.maximize();
        log.info('Interface Odoo affichée');
      });

      this.odooWindow.on('closed', () => {
        this.odooWindow = null;
        log.info('Fenêtre Odoo fermée');

        // Réafficher la fenêtre de connexion si elle existe
        if (this.loginWindow && !this.loginWindow.isDestroyed()) {
          this.loginWindow.show();
        }
      });

      // Surveiller le chargement de la page
      this.odooWindow.webContents.on('did-finish-load', () => {
        log.info('Interface Odoo entièrement chargée');

        // Vérifier si Odoo est prêt
        this.checkOdooReady();
      });

      this.odooWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription) => {
        log.error(`Échec du chargement de l'interface Odoo: ${errorDescription} (${errorCode})`);
      });

      // Désactiver le menu par défaut
      this.odooWindow.setMenu(null);

      return this.odooWindow;

    } catch (error) {
      log.error('Erreur lors de la création de la fenêtre Odoo:', error);
      throw error;
    }
  }

  /**
   * Configurer la session pour Odoo
   * @param {Object} odooData - Données de connexion Odoo
   * @returns {Promise<void>}
   */
  async setupOdooSession(odooData) {
    try {
      log.info('Configuration de la session Odoo...');

      // Configurer la session avec des paramètres optimisés pour Odoo
      const ses = this.odooWindow.webContents.session;

      // Nettoyer les données existantes
      await ses.clearStorageData({
        storages: ['cookies', 'localstorage', 'sessionstorage', 'cachestorage']
      });

      // Configurer les permissions
      ses.setPermissionRequestHandler((webContents, permission, callback) => {
        // Autoriser toutes les permissions pour Odoo
        callback(true);
      });

      // Configurer les headers pour éviter les problèmes CORS
      ses.webRequest.onBeforeSendHeaders((details, callback) => {
        details.requestHeaders['User-Agent'] = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36';
        details.requestHeaders['Accept'] = 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8';
        details.requestHeaders['Accept-Language'] = 'fr-FR,fr;q=0.9,en;q=0.8';
        details.requestHeaders['Cache-Control'] = 'no-cache';
        callback({ requestHeaders: details.requestHeaders });
      });

      // Extraire le domaine de l'URL
      const url = new URL(odooData.serverUrl);
      const domain = `${url.protocol}//${url.hostname}${url.port ? `:${url.port}` : ''}`;

      // Définir le cookie de session
      const cookie = {
        url: domain,
        name: 'session_id',
        value: odooData.sessionId,
        path: '/',
        httpOnly: false,
        secure: url.protocol === 'https:',
        sameSite: 'no_restriction'
      };

      await ses.cookies.set(cookie);

      // Vérifier que le cookie a été défini
      const cookies = await ses.cookies.get({ url: domain });
      const sessionCookie = cookies.find(c => c.name === 'session_id');

      if (sessionCookie) {
        log.info(`Cookie de session défini avec succès: ${sessionCookie.value.substring(0, 8)}...`);
      } else {
        log.warn('Échec de la définition du cookie de session');
      }

    } catch (error) {
      log.error('Erreur lors de la configuration de la session Odoo:', error);
      throw error;
    }
  }

  /**
   * Configurer les gestionnaires d'événements pour Odoo
   */
  setupOdooEventHandlers() {
    if (!this.odooWindow || this.odooWindow.isDestroyed()) {
      return;
    }

    // Gérer les erreurs de chargement des ressources
    this.odooWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription, validatedURL) => {
      log.error(`Échec de chargement: ${errorDescription} pour ${validatedURL}`);

      // Réessayer le chargement pour certaines erreurs
      if (errorCode === -106 || errorCode === -118) { // ERR_INTERNET_DISCONNECTED ou ERR_CONNECTION_TIMED_OUT
        log.info('Tentative de rechargement...');
        setTimeout(() => {
          if (!this.odooWindow.isDestroyed()) {
            this.odooWindow.reload();
          }
        }, 2000);
      }
    });

    // Gérer les erreurs de certificat
    this.odooWindow.webContents.on('certificate-error', (event, url, error, certificate, callback) => {
      log.warn(`Erreur de certificat pour ${url}: ${error}`);
      // Accepter les certificats auto-signés pour les serveurs locaux
      if (url.includes('192.168.') || url.includes('localhost') || url.includes('127.0.0.1')) {
        event.preventDefault();
        callback(true);
      } else {
        callback(false);
      }
    });

    // Gérer les nouvelles fenêtres (popups Odoo)
    this.odooWindow.webContents.setWindowOpenHandler(({ url }) => {
      log.info(`Ouverture de nouvelle fenêtre: ${url}`);
      return {
        action: 'allow',
        overrideBrowserWindowOptions: {
          webPreferences: {
            nodeIntegration: false,
            contextIsolation: false,
            webSecurity: false,
            allowRunningInsecureContent: true
          }
        }
      };
    });

    // Injecter du JavaScript pour corriger les problèmes Odoo
    this.odooWindow.webContents.on('dom-ready', () => {
      this.injectOdooFixes();
    });
  }

  /**
   * Injecter des corrections JavaScript pour Odoo
   */
  async injectOdooFixes() {
    try {
      await this.odooWindow.webContents.executeJavaScript(`
        // Corriger les problèmes de chargement des ressources
        (function() {
          // Intercepter les erreurs de chargement
          const originalFetch = window.fetch;
          window.fetch = function(...args) {
            return originalFetch.apply(this, args).catch(error => {
              console.warn('Fetch error intercepted:', error);
              // Réessayer une fois
              return originalFetch.apply(this, args);
            });
          };

          // Corriger les problèmes de Content-Length
          const originalXHR = window.XMLHttpRequest;
          window.XMLHttpRequest = function() {
            const xhr = new originalXHR();
            const originalOpen = xhr.open;
            xhr.open = function(method, url, ...args) {
              // Ajouter des headers pour éviter les problèmes
              const result = originalOpen.apply(this, [method, url, ...args]);
              this.setRequestHeader('Cache-Control', 'no-cache');
              return result;
            };
            return xhr;
          };

          // Attendre que Odoo soit chargé
          let checkOdoo = setInterval(() => {
            if (typeof odoo !== 'undefined' && odoo.define) {
              console.log('Odoo détecté et fonctionnel');
              clearInterval(checkOdoo);
            } else if (document.readyState === 'complete') {
              // Si Odoo n'est pas chargé après 10 secondes, recharger
              setTimeout(() => {
                if (typeof odoo === 'undefined') {
                  console.log('Rechargement de la page - Odoo non détecté');
                  window.location.reload();
                }
              }, 10000);
              clearInterval(checkOdoo);
            }
          }, 500);
        })();
      `);
    } catch (error) {
      log.error('Erreur lors de l\'injection des corrections:', error);
    }
  }

  /**
   * Vérifier si l'interface Odoo est prête
   * @returns {Promise<void>}
   */
  async checkOdooReady() {
    try {
      if (!this.odooWindow || this.odooWindow.isDestroyed()) {
        return;
      }

      // Exécuter du JavaScript dans la page pour vérifier si Odoo est prêt
      const isReady = await this.odooWindow.webContents.executeJavaScript(`
        (function() {
          // Vérifier la présence d'éléments spécifiques à Odoo
          const odooElements = [
            document.querySelector('.o_main_navbar'),
            document.querySelector('.o_action_manager'),
            document.querySelector('#oe_main_menu_navbar')
          ];

          // Vérifier si au moins un élément Odoo est présent
          const hasOdooElements = odooElements.some(el => el !== null);

          // Vérifier si la page n'est pas en cours de chargement
          const isNotLoading = !document.querySelector('.o_loading');

          return hasOdooElements && isNotLoading;
        })();
      `);

      if (isReady) {
        log.info('Interface Odoo prête et fonctionnelle');

        // Masquer l'écran de chargement
        await this.hideSplashScreen();

        // Émettre un événement pour notifier que Odoo est prêt
        if (this.odooWindow && !this.odooWindow.isDestroyed()) {
          this.odooWindow.webContents.send('odoo-ready');
        }
      } else {
        // Réessayer après un délai
        setTimeout(() => this.checkOdooReady(), 1000);
      }

    } catch (error) {
      log.error('Erreur lors de la vérification de l\'état d\'Odoo:', error);

      // En cas d'erreur, masquer l'écran de chargement après un délai
      setTimeout(async () => {
        await this.hideSplashScreen();
      }, 3000);
    }
  }

  /**
   * Obtenir la fenêtre active
   * @returns {BrowserWindow|null} - Fenêtre active ou null
   */
  getActiveWindow() {
    if (this.odooWindow && !this.odooWindow.isDestroyed()) {
      return this.odooWindow;
    } else if (this.loginWindow && !this.loginWindow.isDestroyed()) {
      return this.loginWindow;
    } else if (this.splashWindow && !this.splashWindow.isDestroyed()) {
      return this.splashWindow;
    }
    return null;
  }

  /**
   * Fermer toutes les fenêtres
   * @returns {Promise<void>}
   */
  async closeAllWindows() {
    try {
      log.info('Fermeture de toutes les fenêtres...');

      if (this.odooWindow && !this.odooWindow.isDestroyed()) {
        this.odooWindow.close();
      }

      if (this.splashWindow && !this.splashWindow.isDestroyed()) {
        this.splashWindow.close();
      }

      if (this.loginWindow && !this.loginWindow.isDestroyed()) {
        this.loginWindow.close();
      }

    } catch (error) {
      log.error('Erreur lors de la fermeture des fenêtres:', error);
    }
  }
}

module.exports = WindowManager;

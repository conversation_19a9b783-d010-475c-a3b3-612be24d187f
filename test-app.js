/**
 * Script de test pour l'application Edara Electron
 * Teste les modules principaux sans lancer l'interface graphique
 */

const path = require('path');
const fs = require('fs');

// Simuler l'environnement Electron pour les tests
global.require = require;

console.log('🧪 Test de l\'application Edara Electron\n');

/**
 * Tester un module
 */
async function testModule(moduleName, modulePath, testFunction) {
  try {
    console.log(`📦 Test du module: ${moduleName}`);

    // Vérifier que le fichier existe
    if (!fs.existsSync(modulePath)) {
      throw new Error(`Fichier non trouvé: ${modulePath}`);
    }

    // Charger le module
    const Module = require(modulePath);

    // Exécuter le test
    if (testFunction) {
      await testFunction(Module);
    }

    console.log(`✅ ${moduleName}: OK\n`);
    return true;

  } catch (error) {
    console.log(`❌ ${moduleName}: ERREUR`);
    console.log(`   ${error.message}\n`);
    return false;
  }
}

/**
 * Tests des modules
 */
async function runTests() {
  let passedTests = 0;
  let totalTests = 0;

  // Test ConnectionManager
  totalTests++;
  const connectionManagerPassed = await testModule(
    'ConnectionManager',
    './src/main/connection-manager.js',
    async (ConnectionManager) => {
      const manager = new ConnectionManager();

      // Test de la méthode loadServerConfig
      await manager.loadServerConfig();

      if (!Array.isArray(manager.localServers)) {
        throw new Error('localServers doit être un tableau');
      }

      if (typeof manager.remoteServerUrl !== 'string') {
        throw new Error('remoteServerUrl doit être une chaîne');
      }

      console.log(`   - Serveurs locaux configurés: ${manager.localServers.length}`);
      console.log(`   - Serveur distant: ${manager.remoteServerUrl}`);
    }
  );
  if (connectionManagerPassed) passedTests++;

  // Test OdooAuth
  totalTests++;
  const odooAuthPassed = await testModule(
    'OdooAuth',
    './src/main/odoo-auth.js',
    async (OdooAuth) => {
      const auth = new OdooAuth();

      if (typeof auth.defaultDatabase !== 'string') {
        throw new Error('defaultDatabase doit être une chaîne');
      }

      if (typeof auth.authTimeout !== 'number') {
        throw new Error('authTimeout doit être un nombre');
      }

      // Test de la méthode buildAuthRequest
      const xmlRequest = auth.buildAuthRequest('test', 'password', 'db');
      if (!xmlRequest.includes('<methodName>authenticate</methodName>')) {
        throw new Error('Requête XML malformée');
      }

      console.log(`   - Base de données par défaut: ${auth.defaultDatabase}`);
      console.log(`   - Timeout d'authentification: ${auth.authTimeout}ms`);
    }
  );
  if (odooAuthPassed) passedTests++;

  // Test WindowManager (sans créer de fenêtres)
  totalTests++;
  const windowManagerPassed = await testModule(
    'WindowManager',
    './src/main/window-manager.js',
    async (WindowManager) => {
      const manager = new WindowManager();

      // Vérifier que les propriétés existent
      if (typeof manager.loginWindow !== 'undefined' && manager.loginWindow !== null) {
        throw new Error('loginWindow doit être null au démarrage');
      }

      if (typeof manager.splashWindow !== 'undefined' && manager.splashWindow !== null) {
        throw new Error('splashWindow doit être null au démarrage');
      }

      if (typeof manager.odooWindow !== 'undefined' && manager.odooWindow !== null) {
        throw new Error('odooWindow doit être null au démarrage');
      }

      console.log('   - Gestionnaire de fenêtres initialisé correctement');
    }
  );
  if (windowManagerPassed) passedTests++;

  // Test de la configuration
  totalTests++;
  const configPassed = await testModule(
    'Configuration',
    './config/app-config.js',
    async (config) => {
      // Vérifier la structure de la configuration
      const requiredSections = ['servers', 'database', 'windows', 'logging', 'app'];

      for (const section of requiredSections) {
        if (!config[section]) {
          throw new Error(`Section manquante dans la configuration: ${section}`);
        }
      }

      // Vérifier quelques valeurs importantes
      if (typeof config.servers.localPort !== 'number') {
        throw new Error('servers.localPort doit être un nombre');
      }

      if (typeof config.servers.remoteUrl !== 'string') {
        throw new Error('servers.remoteUrl doit être une chaîne');
      }

      if (typeof config.database.defaultName !== 'string') {
        throw new Error('database.defaultName doit être une chaîne');
      }

      console.log(`   - Port local: ${config.servers.localPort}`);
      console.log(`   - URL distante: ${config.servers.remoteUrl}`);
      console.log(`   - Base de données: ${config.database.defaultName}`);
    }
  );
  if (configPassed) passedTests++;

  // Test des fichiers HTML
  totalTests++;
  let htmlFilesPassed = false;
  try {
    console.log('📦 Test du module: Fichiers HTML');

    // Vérifier que les fichiers HTML existent et contiennent les éléments requis
    const indexContent = fs.readFileSync('./index-final.html', 'utf8');
    const splashContent = fs.readFileSync('./splash-final.html', 'utf8');

    // Vérifications pour index-final.html
    if (!indexContent.includes('edara-login-form')) {
      throw new Error('Formulaire de connexion non trouvé dans index-final.html');
    }

    if (!indexContent.includes('src/renderer/login-handler.js')) {
      throw new Error('Script login-handler.js non référencé dans index-final.html');
    }

    // Vérifications pour splash-final.html
    if (!splashContent.includes('splash-container')) {
      throw new Error('Conteneur splash non trouvé dans splash-final.html');
    }

    if (!splashContent.includes('progress-bar')) {
      throw new Error('Barre de progression non trouvée dans splash-final.html');
    }

    console.log('   - index-final.html: Structure valide');
    console.log('   - splash-final.html: Structure valide');
    console.log('✅ Fichiers HTML: OK\n');
    htmlFilesPassed = true;

  } catch (error) {
    console.log('❌ Fichiers HTML: ERREUR');
    console.log(`   ${error.message}\n`);
    htmlFilesPassed = false;
  }
  if (htmlFilesPassed) passedTests++;

  // Résumé des tests
  console.log('📊 Résumé des tests:');
  console.log(`   Tests réussis: ${passedTests}/${totalTests}`);

  if (passedTests === totalTests) {
    console.log('✅ Tous les tests sont passés avec succès !');
    console.log('🚀 L\'application est prête à être lancée.');
    return true;
  } else {
    console.log('❌ Certains tests ont échoué.');
    console.log('🔧 Veuillez corriger les erreurs avant de continuer.');
    return false;
  }
}

// Exécuter les tests
runTests()
  .then((success) => {
    process.exit(success ? 0 : 1);
  })
  .catch((error) => {
    console.error('💥 Erreur lors de l\'exécution des tests:', error);
    process.exit(1);
  });
